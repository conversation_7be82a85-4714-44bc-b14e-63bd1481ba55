package com.kikitrade.activity.service.draw;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 奖励发放策略工厂
 * 根据物品类型选择对应的发放策略
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class RewardIssueStrategyFactory {

    @Resource
    private List<RewardIssueStrategy> strategies;

    private final Map<String, RewardIssueStrategy> strategyMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        for (RewardIssueStrategy strategy : strategies) {
            // 由于每个策略可能支持多种类型，需要遍历检查
            // 这里使用一个简化的方式，实际应用中可能需要更复杂的注册机制
            registerStrategy(strategy);
        }
        log.info("奖励发放策略注册完成，共注册{}个策略", strategyMap.size());
    }

    private void registerStrategy(RewardIssueStrategy strategy) {
        // 常见的物品类型
        String[] itemTypes = {"ITEM", "CURRENCY", "GIFT_PACK", "AVATAR_FRAME", "VIP_CARD"};
        
        for (String itemType : itemTypes) {
            if (strategy.supports(itemType)) {
                strategyMap.put(itemType, strategy);
                log.debug("注册策略: {} -> {}", itemType, strategy.getClass().getSimpleName());
            }
        }
    }

    /**
     * 根据物品类型获取对应的发放策略
     * 
     * @param itemType 物品类型
     * @return 发放策略
     */
    public RewardIssueStrategy getStrategy(String itemType) {
        RewardIssueStrategy strategy = strategyMap.get(itemType);
        if (strategy == null) {
            log.warn("未找到物品类型{}对应的发放策略", itemType);
        }
        return strategy;
    }

    /**
     * 检查是否支持某种物品类型的发放
     * 
     * @param itemType 物品类型
     * @return 是否支持
     */
    public boolean supports(String itemType) {
        return strategyMap.containsKey(itemType);
    }
}
