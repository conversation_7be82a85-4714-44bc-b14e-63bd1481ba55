package com.kikitrade.activity.service.draw.impl;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.http.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.kikitrade.activity.api.exception.ActivityException;
import com.kikitrade.activity.api.exception.ActivityExceptionType;
import com.kikitrade.activity.api.model.draw.ExchangeTicketsDTO;
import com.kikitrade.activity.api.model.request.reward.ExchangeTicketsRequest;
import com.kikitrade.activity.api.model.response.reward.ExchangeTicketsResponse;
import com.kikitrade.activity.dal.tablestore.builder.DirectRewardIssuanceLogBuilder;
import com.kikitrade.activity.dal.tablestore.builder.PrizePoolBuilder;
import com.kikitrade.activity.dal.tablestore.model.DirectRewardIssuanceLog;
import com.kikitrade.activity.dal.tablestore.model.PrizePool;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ExchangeTypeConstant;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.draw.LotteryTicketService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 抽奖券管理服务实现
 * 基于UserLotteryProfile存储抽奖券信息，支持两阶段抽奖系统
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class LotteryTicketServiceImpl implements LotteryTicketService {
    
    @Resource
    private PrizePoolBuilder prizePoolBuilder;

    @Resource
    private KactivityProperties kactivityProperties;

    @Resource
    private DirectRewardIssuanceLogBuilder directRewardIssuanceLogBuilder;

    // 重试配置
    private static final int MAX_RETRY_TIMES = 3;
    private static final long RETRY_DELAY_MS = 1000;

    @Override
    public ExchangeTicketsResponse exchangeTickets(ExchangeTicketsRequest request) {
        try {
            // 1. 参数验证
            if (!validateExchangeRequest(request)) {
                return ExchangeTicketsResponse.builder().success(false).errorCode("INVALID_PARAMS")
                    .message("请求参数无效").build();
            }
            ExchangeTicketsDTO exchangeTicketsDTO =
                ExchangeTicketsDTO.builder().userId(request.getCustomerId()).saasId(request.getSaasId())
                    .prizePoolCode(request.getPrizePoolCode()).exchangeType(request.getExchangeType())
                    .transferOutAssetType(request.getAssetType()).optionId(request.getOptionId())
                    .businessType("ACTIVITY_LOTTERY").transferInAssetType("TICKET_" + request.getPrizePoolCode())
                    .build();

            // 4. 计算抽奖券数量
            calculateTicketCount(exchangeTicketsDTO);

            // 5. 执行兑换操作
            boolean success = performExchange(exchangeTicketsDTO);
            if (success) {
                return ExchangeTicketsResponse.builder().success(true)
                    .ticketType(exchangeTicketsDTO.getTransferInAssetType())
                    .ticketsObtained(exchangeTicketsDTO.getTransferInCount())
                    .assetType(exchangeTicketsDTO.getTransferOutAssetType()).message("兑换成功").build();
            } else {
                return ExchangeTicketsResponse.builder().success(false).errorCode("EXCHANGE_FAILED").message("兑换失败")
                    .build();
            }

        } catch (ActivityException e) {
            log.error("兑换抽奖券异常: userId={}", request.getCustomerId(), e);
            return ExchangeTicketsResponse.builder()
                    .success(false)
                    .errorCode(e.getCode())
                    .message(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("兑换抽奖券异常: userId={}", request.getCustomerId(), e);
            return ExchangeTicketsResponse.builder()
                    .success(false)
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }

    @Override
    public boolean consumeTickets(String userId, String saasId, String prizePoolCode, Integer count) {
        log.info("消费抽奖券: userId={}, transferInAssetType={}, count={}", userId, prizePoolCode, count);
        
        try {
            ExchangeTicketsDTO exchangeTicketsDTO =
                ExchangeTicketsDTO.builder()
                    .userId(userId)
                    .saasId(saasId)
                    .transferOutAssetType("TICKET_" + prizePoolCode)
                    .transferOutAmount(String.valueOf(count))
                    .businessType("SPIN_A_WHEEL")
                    .desc("consume tickets")
                    .build();
            return transferOut(exchangeTicketsDTO);
        } catch (Exception e) {
            log.error("消费抽奖券异常: userId={}, transferOutAssetType={}, count={}", userId, "TICKET_" + prizePoolCode, count, e);
            return false;
        }
    }
    
    @Override
    public boolean refundTickets(String userId, String saasId, String prizePoolCode, Integer count) {
        log.info("退还抽奖券: userId={}, transferInAssetType={}, count={}", userId, prizePoolCode, count);
        
        try {
            ExchangeTicketsDTO exchangeTicketsDTO =
                ExchangeTicketsDTO.builder()
                    .userId(userId)
                    .saasId(saasId)
                    .transferInCount(count)
                    .transferInAssetType("TICKET_" + prizePoolCode)
                    .businessType("SPIN_A_WHEEL")
                    .desc("refund tickets")
                    .build();
            return transferIn(exchangeTicketsDTO);
        } catch (Exception e) {
            log.error("退还抽奖券异常: userId={}, transferInAssetType={}, count={}", userId, "TICKET_" + prizePoolCode, count, e);
            return false;
        }
    }
    
    /**
     * 验证兑换请求参数
     */
    private boolean validateExchangeRequest(ExchangeTicketsRequest request) {
        if (request == null || !StringUtils.hasText(request.getCustomerId()) || !StringUtils.hasText(request.getSaasId()) || !StringUtils.hasText(request.getPrizePoolCode())
            || !StringUtils.hasText(request.getExchangeType()) || !StringUtils.hasText(request.getAssetType()) || !StringUtils.hasText(request.getOptionId())) {
            log.error("兑换请求参数无效: {}", JSON.toJSONString(request));
            return false;
        }
        return true;
    }
    
    /**
     * 执行兑换操作，支持策略模式和重试机制
     */
    private boolean performExchange(ExchangeTicketsDTO exchangeTicketsDTO) {
        // 1. 检查是否已经处理过（幂等性）
//        DirectRewardIssuanceLog existingLog = directRewardIssuanceLogBuilder.findByTransactionId(exchangeTicketsDTO.getBusinessId());
//        if (existingLog != null) {
//            if (ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name().equals(existingLog.getIssueStatus())) {
//                log.info("兑换已成功处理，跳过重复操作: businessId={}", exchangeTicketsDTO.getBusinessId());
//                return true;
//            } else if (ActivityConstant.RewardStatusEnum.AWARDING.name().equals(existingLog.getIssueStatus())) {
//                log.warn("兑换正在处理中，请稍后重试: businessId={}", exchangeTicketsDTO.getBusinessId());
//                return false;
//            }
//        }
//
//        // 2. 创建处理中的流水记录
//        saveExchangeLog(exchangeTicketsDTO, ActivityConstant.RewardStatusEnum.AWARDING.name(), null, null);

        // 3. 根据兑换类型选择处理策略
        ExchangeStrategy strategy = getExchangeStrategy(exchangeTicketsDTO.getExchangeType());

        try {
            // 4. 执行兑换操作
            boolean success = strategy.execute(exchangeTicketsDTO);

            // 5. 更新流水记录
//            String status = success ? ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name() : ActivityConstant.RewardStatusEnum.AWARD_FAILED.name();
//            String failureReason = success ? null : "兑换操作失败";
//            saveExchangeLog(exchangeTicketsDTO, status, null, failureReason);

            return success;

        } catch (ActivityException e) {
            log.error("执行兑换操作异常: userId={}, businessId={}",
                    exchangeTicketsDTO.getUserId(), exchangeTicketsDTO.getBusinessId(), e);
            // 更新流水记录为失败状态
//            saveExchangeLog(exchangeTicketsDTO, ActivityConstant.RewardStatusEnum.AWARD_FAILED.name(), null, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("执行兑换操作异常: userId={}, businessId={}",
                    exchangeTicketsDTO.getUserId(), exchangeTicketsDTO.getBusinessId(), e);

            // 更新流水记录为失败状态
//            saveExchangeLog(exchangeTicketsDTO, ActivityConstant.RewardStatusEnum.AWARD_FAILED.name(), null, "系统异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取兑换策略
     */
    private ExchangeStrategy getExchangeStrategy(String exchangeType) {
        if (ExchangeTypeConstant.EXCHANGE_TICKETS_AND_WAITING_TRANSFER.equals(exchangeType)) {
            return new WaitingTransferExchangeStrategy();
        }
        if (ExchangeTypeConstant.EXCHANGE_TICKETS_CONSUME.equals(exchangeType)) {
            return new ConsumeTicketStrategy();
        }
        // 可以在这里添加更多策略
        // if (ExchangeTypeConstant.EXCHANGE_TICKETS_VIP.equals(exchangeType)) {
        //     return new VipExchangeStrategy();
        // }

        // 默认策略
        return new DefaultExchangeStrategy();
    }

    /**
     * 保存兑换流水记录
     */
    private void saveExchangeLog(ExchangeTicketsDTO exchangeTicketsDTO, String status,
                                String rewardsContent, String failureReason) {
//        try {
//            DirectRewardIssuanceLog log = new DirectRewardIssuanceLog();
//            log.setTransactionId(exchangeTicketsDTO.getBusinessId());
//            log.setUserId(exchangeTicketsDTO.getUserId());
//            log.setSaasId(exchangeTicketsDTO.getSaasId());
//            log.setChannel("LOTTERY_EXCHANGE");
//            log.setIssueStatus(status);
//            log.setDescription("抽奖券兑换: " + exchangeTicketsDTO.getExchangeType());
//            log.setRewardsContent(rewardsContent != null ? rewardsContent :
//                String.format("兑换类型:%s,资产类型:%s,消耗:%d,获得抽奖券:%d",
//                    exchangeTicketsDTO.getExchangeType(),
//                    exchangeTicketsDTO.getTransferOutAssetType(),
//                    exchangeTicketsDTO.getTransferOutAmount(),
//                    exchangeTicketsDTO.getTransferInCount()));
//            log.setFailureReason(failureReason);
//            log.setCreateTime(System.currentTimeMillis());
//            log.setUpdateTime(System.currentTimeMillis());
//
//            directRewardIssuanceLogBuilder.insert(log);
//        } catch (Exception e) {
//            log.error("保存兑换流水记录失败: businessId={}", exchangeTicketsDTO.getBusinessId(), e);
//        }
    }

    /**
     * 带重试机制的transfer方法
     */
    private boolean transfer(ExchangeTicketsDTO exchangeTicketsDTO) {
        return executeWithRetry("transfer", () -> doTransfer(exchangeTicketsDTO));
    }

    /**
     * 执行transfer操作
     */
    private boolean doTransfer(ExchangeTicketsDTO exchangeTicketsDTO) {
        //这里要构造http请求调用其他服务
        String transferUrl = kactivityProperties.getQuestsApiHost() + "/s2/member/transfer";
        HttpRequest httpRequest = HttpUtil.createPost(transferUrl);
        httpRequest.addHeaders(buildHeaders(kactivityProperties.getSaasId()));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("saasId", exchangeTicketsDTO.getSaasId());
        jsonObject.put("amount", String.valueOf(exchangeTicketsDTO.getTransferOutAmount()));
        jsonObject.put("fromCustomerId", exchangeTicketsDTO.getUserId());
        jsonObject.put("fromAssetType", exchangeTicketsDTO.getTransferOutAssetType());
        jsonObject.put("businessId", generateOrderId(ActivityConstant.AssetOperateType.TRANSFER_OUT, exchangeTicketsDTO.getUserId()));
        jsonObject.put("businessType", exchangeTicketsDTO.getBusinessType());
        jsonObject.put("toAssetType", "WAITING_TRANSFER");
        jsonObject.put("toCustomerId", exchangeTicketsDTO.getUserId());
        jsonObject.put("desc", exchangeTicketsDTO.getDesc());

        httpRequest.body(jsonObject.toJSONString(), ContentType.JSON.toString());

        HttpResponse response = httpRequest.execute();
        if (response == null || response.getStatus() != HttpStatus.HTTP_OK) {
            log.error("transfer调用失败: response={}", JSON.toJSONString(response));
            throw new RuntimeException("transfer调用失败: " + (response != null ? response.getStatus() : "null response"));
        }

        // 解析响应判断是否成功
        if (org.apache.commons.lang3.StringUtils.isBlank(response.body())) {
            throw new RuntimeException("transfer响应为空");
        }

        try {
            JSONObject responseJson = JSON.parseObject(response.body());
            // 根据实际API响应格式判断成功与否
            if (responseJson.containsKey("success") && !responseJson.getBoolean("success")) {
                String errorMsg = responseJson.getString("msgKey");
                String code = responseJson.getString("code");
                // 余额不足等业务异常不需要重试
                if ("4004".equals(code)) {
                    throw new ActivityException(ActivityExceptionType.LOTTERY_INSUFFICIENT_BALANCE);
                }
                throw new RuntimeException("transfer业务失败: " + errorMsg);
            }
        } catch (ActivityException e) {
            throw e; // 业务异常直接抛出，不重试
        } catch (Exception e) {
            log.warn("解析transfer响应异常，默认为成功: {}", e.getMessage());
        }

        return true;
    }

    /**
     * 带重试机制的transferIn方法
     */
    private boolean transferIn(ExchangeTicketsDTO exchangeTicketsDTO) {
        return executeWithRetry("transferIn", () -> doTransferIn(exchangeTicketsDTO));
    }

    /**
     * 执行transferIn操作
     */
    private boolean doTransferIn(ExchangeTicketsDTO exchangeTicketsDTO) {
        //这里要构造http请求调用其他服务
        //2. 增加抽奖券
        String transferInUrl = kactivityProperties.getQuestsApiHost() + "/s2/member/transferin";
        HttpRequest httpRequest = HttpUtil.createPost(transferInUrl);
        httpRequest.addHeaders(buildHeaders(kactivityProperties.getSaasId()));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("saasId", exchangeTicketsDTO.getSaasId());
        jsonObject.put("amount", String.valueOf(exchangeTicketsDTO.getTransferInCount())); // 使用抽奖券数量
        jsonObject.put("customerId", exchangeTicketsDTO.getUserId());
        jsonObject.put("businessId", generateOrderId(ActivityConstant.AssetOperateType.TRANSFER_IN, exchangeTicketsDTO.getUserId()));
        jsonObject.put("businessType", exchangeTicketsDTO.getBusinessType());
        jsonObject.put("type", exchangeTicketsDTO.getTransferInAssetType());
        jsonObject.put("desc", exchangeTicketsDTO.getDesc());

        httpRequest.body(jsonObject.toJSONString(), ContentType.JSON.toString());

        HttpResponse response = httpRequest.execute();
        if (response == null || response.getStatus() != HttpStatus.HTTP_OK) {
            log.error("transferIn调用失败: response={}", JSON.toJSONString(response));
            throw new RuntimeException("transferIn调用失败: " + (response != null ? response.getStatus() : "null response"));
        }

        if (org.apache.commons.lang3.StringUtils.isBlank(response.body())) {
            throw new RuntimeException("transferIn响应为空");
        }

        try {
            JSONObject responseJson = JSON.parseObject(response.body());
            if (responseJson.containsKey("success") && !responseJson.getBoolean("success")) {
                String errorMsg = responseJson.getString("message");
                throw new RuntimeException("transferIn业务失败: " + errorMsg);
            }
        } catch (Exception e) {
            log.warn("解析transferIn响应异常，默认为成功: {}", e.getMessage());
        }

        return true;
    }

    private boolean transferOut(ExchangeTicketsDTO exchangeTicketsDTO) {
        return executeWithRetry("transferOut", () -> doTransferOut(exchangeTicketsDTO));
    }

    private boolean doTransferOut(ExchangeTicketsDTO exchangeTicketsDTO) {
        //这里要构造http请求调用其他服务
        String transferOutUrl = kactivityProperties.getQuestsApiHost() + "/s2/member/transferout";
        HttpRequest httpRequest = HttpUtil.createPost(transferOutUrl);
        httpRequest.addHeaders(buildHeaders(kactivityProperties.getSaasId()));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("saasId", exchangeTicketsDTO.getSaasId());
        jsonObject.put("amount", String.valueOf(exchangeTicketsDTO.getTransferOutAmount()));
        jsonObject.put("customerId", exchangeTicketsDTO.getUserId());
        jsonObject.put("businessId", generateOrderId(ActivityConstant.AssetOperateType.TRANSFER_OUT, exchangeTicketsDTO.getUserId()));
        jsonObject.put("businessType", exchangeTicketsDTO.getBusinessType());
        jsonObject.put("type", exchangeTicketsDTO.getTransferOutAssetType());
        jsonObject.put("desc", exchangeTicketsDTO.getDesc());

        httpRequest.body(jsonObject.toJSONString(), ContentType.JSON.toString());

        HttpResponse response = httpRequest.execute();
        if (response == null || response.getStatus() != HttpStatus.HTTP_OK) {
            log.error("transferOut调用失败: response={}", JSON.toJSONString(response));
            throw new RuntimeException("transferOut调用失败: " + (response != null ? response.getStatus() : "null response"));
        }

        if (org.apache.commons.lang3.StringUtils.isBlank(response.body())) {
            throw new RuntimeException("transferOut响应为空");
        }

        try {
            JSONObject responseJson = JSON.parseObject(response.body());
            if (responseJson.containsKey("success") && !responseJson.getBoolean("success")) {
                String code = responseJson.getString("code");
                // 余额不足等业务异常需要向上传递
                if ("4004".equals(code)) {
                    throw new ActivityException(ActivityExceptionType.LOTTERY_INSUFFICIENT_BALANCE);
                }
                String errorMsg = responseJson.getString("msgKey");
                throw new RuntimeException("transferOut业务失败: " + errorMsg);
            }
        } catch (ActivityException e) {
            // 业务异常需要继续向上抛出
            throw e;
        } catch (Exception e) {
            log.warn("解析transferOut响应异常，默认为成功: {}", e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 通用重试机制
     */
    private boolean executeWithRetry(String operationName, RetryableOperation operation) {
        int retryCount = 0;
        Exception lastException = null;

        while (retryCount <= MAX_RETRY_TIMES) {
            try {
                return operation.execute();
            } catch (BusinessException e) {
                // 业务异常不重试
                log.error("{}操作业务异常，不重试: {}", operationName, e.getMessage());
                return false;
            } catch (Exception e) {
                lastException = e;
                retryCount++;

                if (retryCount <= MAX_RETRY_TIMES) {
                    log.warn("{}操作失败，第{}次重试: {}", operationName, retryCount, e.getMessage());
                    try {
                        TimeUnit.MILLISECONDS.sleep(RETRY_DELAY_MS * retryCount); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("重试延迟被中断");
                        return false;
                    }
                } else {
                    log.error("{}操作重试{}次后仍然失败", operationName, MAX_RETRY_TIMES, e);
                }
            }
        }

        return false;
    }

    @NotNull
    private HashMap<String, String> buildHeaders(String saasId) {
        BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
        long current = System.currentTimeMillis();
        String appKey = bCryptPasswordEncoder.encode(String.format(kactivityProperties.getQuestsAppKeySuffix(), saasId, current));
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Authorization", Base64Utils.encodeToString((current + ":" + appKey).getBytes()));
        headers.put("saas_id", saasId);
        return headers;
    }
    
    /**
     * 从奖池配置中计算抽奖券数量
     */
    private void calculateTicketCount(ExchangeTicketsDTO exchangeTicketsDTO) {
        log.info("计算抽奖券数量: exchangeTicketsDTO={}", JSON.toJSONString(exchangeTicketsDTO));
        try {
            // 1. 获取奖池配置
            PrizePool prizePool = prizePoolBuilder.findByCodeAndSaasId(exchangeTicketsDTO.getPrizePoolCode(), exchangeTicketsDTO.getSaasId());
            if (prizePool == null) {
                log.warn("奖池不存在: prizePoolCode={}, saasId={}", exchangeTicketsDTO.getPrizePoolCode(), exchangeTicketsDTO.getSaasId());
                return;
            }
            // 2. 解析兑换规则JSON
            List<ExchangeRule> exchangeRules = parseExchangeRules(prizePool.getExchangeRules());
            if (exchangeRules == null || exchangeRules.isEmpty()) {
                log.warn("奖池兑换规则为空: prizePoolCode={}", exchangeTicketsDTO.getPrizePoolCode());
                return;
            }
            // 3. 查找匹配的资产类型和兑换类型
            for (ExchangeRule rule : exchangeRules) {
                if (exchangeTicketsDTO.getTransferOutAssetType().equals(rule.getAssetType()) && exchangeTicketsDTO.getExchangeType().equals(rule.getExchangeType())) {
                    // 从options中找到匹配的选项
                    if (rule.getOptions() != null && !rule.getOptions().isEmpty()) {
                        for (ExchangeOption option : rule.getOptions()) {
                            if (exchangeTicketsDTO.getOptionId().equals(option.getOptionId())) {
                                exchangeTicketsDTO.setTransferOutAmount(option.getCost());
                                exchangeTicketsDTO.setTransferInCount(option.getTickets());
                                exchangeTicketsDTO.setDesc(option.getDescription());
                            }
                        }
                    }
                }
            }
            log.warn("未找到匹配的兑换规则");
        } catch (Exception e) {
            log.error("计算抽奖券数量异常", e);
        }
    }

    /**
     * 解析兑换规则JSON
     */
    private List<ExchangeRule> parseExchangeRules(String exchangeRulesJson) {
        if (!StringUtils.hasText(exchangeRulesJson)) {
            return null;
        }

        return JSON.parseObject(exchangeRulesJson, new TypeReference<List<Map<String, Object>>>(){});
    }

    /**
     * 生成兑换订单ID
     */
    private String generateOrderId(ActivityConstant.AssetOperateType type, String userId) {
        String timestamp = OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return timestamp + type.getCode() + userId;
    }

    /**
     * 兑换规则内部类
     */
    @Data
    private static class ExchangeRule {
        private String exchangeType;
        private String assetType;
        private List<ExchangeOption> options;

        // 为了向后兼容，保留getCost方法
        public String getCost() {
            if (options != null && !options.isEmpty()) {
                return options.get(0).getCost();
            }
            return null;
        }
    }

    /**
     * 兑换选项内部类
     */
    @Data
    private static class ExchangeOption {
        private String optionId;
        private String cost;
        private Integer tickets;
        private String description;
    }



    /**
     * 重试操作接口
     */
    @FunctionalInterface
    private interface RetryableOperation {
        boolean execute() throws Exception;
    }

    /**
     * 业务异常类（不需要重试的异常）
     */
    private static class BusinessException extends RuntimeException {
        public BusinessException(String message) {
            super(message);
        }
    }

    /**
     * 兑换策略接口
     */
    private interface ExchangeStrategy {
        boolean execute(ExchangeTicketsDTO exchangeTicketsDTO) throws Exception;
    }

    /**
     * 默认兑换策略
     */
    private class DefaultExchangeStrategy implements ExchangeStrategy {
        @Override
        public boolean execute(ExchangeTicketsDTO exchangeTicketsDTO) throws Exception {
            log.info("执行默认兑换策略: userId={}, exchangeType={}",
                    exchangeTicketsDTO.getUserId(), exchangeTicketsDTO.getExchangeType());

            // 先调用transfer方法
            boolean transferSuccess = transfer(exchangeTicketsDTO);
            if (!transferSuccess) {
                log.error("transfer失败: userId={}", exchangeTicketsDTO.getUserId());
                return false;
            }

            // transfer成功后，再调用transferIn方法
            boolean transferInSuccess = transferIn(exchangeTicketsDTO);
            if (!transferInSuccess) {
                log.error("transferIn失败: userId={}", exchangeTicketsDTO.getUserId());
                // 这里可以考虑补偿机制，回滚transfer操作
                return false;
            }

            return true;
        }
    }

    /**
     * 个人联盟积分(待转化积分)兑换策略
     */
    private class WaitingTransferExchangeStrategy implements ExchangeStrategy {
        @Override
        public boolean execute(ExchangeTicketsDTO exchangeTicketsDTO) throws Exception {
            log.info("执行个人联盟积分兑换策略: userId={}, exchangeType={}",
                    exchangeTicketsDTO.getUserId(), exchangeTicketsDTO.getExchangeType());

            // 个人联盟积分兑换的定制逻辑
            // 可以在这里添加特殊的验证、计算或处理逻辑

            // 1. 特殊的积分计算逻辑（如果需要）
            calculatePersonalAllianceBonus(exchangeTicketsDTO);

            // 2. 这里的兑换在扣减积分的同时会增加待转化积分
            boolean transferSuccess = transfer(exchangeTicketsDTO);
            if (!transferSuccess) {
                log.error("个人联盟积分transfer失败: userId={}", exchangeTicketsDTO.getUserId());
                return false;
            }

            boolean transferInSuccess = transferIn(exchangeTicketsDTO);
            if (!transferInSuccess) {
                log.error("个人联盟积分transferIn失败: userId={}", exchangeTicketsDTO.getUserId());
                return false;
            }

            // 3. 个人联盟特有的后续处理（如果需要）
            handlePersonalAlliancePostProcess(exchangeTicketsDTO);

            return true;
        }

        /**
         * 计算个人联盟奖励加成
         */
        private void calculatePersonalAllianceBonus(ExchangeTicketsDTO exchangeTicketsDTO) {
            // 这里可以实现个人联盟的特殊奖励计算逻辑
            // 例如：根据用户等级给予额外的抽奖券
            log.info("计算个人联盟奖励加成: userId={}", exchangeTicketsDTO.getUserId());
        }

        /**
         * 个人联盟后续处理
         */
        private void handlePersonalAlliancePostProcess(ExchangeTicketsDTO exchangeTicketsDTO) {
            // 这里可以实现个人联盟特有的后续处理逻辑
            // 例如：更新用户联盟积分、发送特殊通知等
            log.info("执行个人联盟后续处理: userId={}", exchangeTicketsDTO.getUserId());
        }
    }

    /**
     * 消耗抽奖券策略
     */
    private class ConsumeTicketStrategy implements ExchangeStrategy {
        @Override
        public boolean execute(ExchangeTicketsDTO exchangeTicketsDTO) throws Exception {
            log.info("执行消耗抽奖券策略: userId={}, transferInAssetType={}, count={}",
                    exchangeTicketsDTO.getUserId(), exchangeTicketsDTO.getTransferOutAssetType(), exchangeTicketsDTO.getTransferInCount());

            // 1. 调用transferOut方法
            boolean transferOutSuccess = transferOut(exchangeTicketsDTO);
            if (!transferOutSuccess) {
                log.error("transferOut失败: userId={}", exchangeTicketsDTO.getUserId());
                return false;
            }

            return true;
        }
    }

    @Override
    public boolean freezeAsset(ExchangeTicketsDTO exchangeTicketsDTO) {
        return executeWithRetry("freezeAsset", () -> doFreezeAsset(exchangeTicketsDTO));
    }

    private boolean doFreezeAsset(ExchangeTicketsDTO exchangeTicketsDTO) {
        //这里要构造http请求调用其他服务
        String transferFreezeUrl = kactivityProperties.getQuestsApiHost() + "/s2/member/freeze";
        HttpRequest httpRequest = HttpUtil.createPost(transferFreezeUrl);
        httpRequest.addHeaders(buildHeaders(kactivityProperties.getSaasId()));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("saasId", exchangeTicketsDTO.getSaasId());
        jsonObject.put("amount", String.valueOf(exchangeTicketsDTO.getTransferOutAmount()));
        jsonObject.put("customerId", exchangeTicketsDTO.getUserId());
        jsonObject.put("businessId", exchangeTicketsDTO.getBusinessId());
        jsonObject.put("businessType", exchangeTicketsDTO.getBusinessType());
        jsonObject.put("type", exchangeTicketsDTO.getTransferOutAssetType());
        jsonObject.put("desc", exchangeTicketsDTO.getDesc());

        httpRequest.body(jsonObject.toJSONString(), ContentType.JSON.toString());

        HttpResponse response = httpRequest.execute();
        if (response == null || response.getStatus() != HttpStatus.HTTP_OK) {
            log.error("doFreezeAsset调用失败: response={}", JSON.toJSONString(response));
            throw new RuntimeException("transferOut调用失败: " + (response != null ? response.getStatus() : "null response"));
        }

        if (org.apache.commons.lang3.StringUtils.isBlank(response.body())) {
            throw new RuntimeException("doFreezeAsset响应为空");
        }

        try {
            JSONObject responseJson = JSON.parseObject(response.body());
            if (responseJson.containsKey("success") && !responseJson.getBoolean("success")) {
                String code = responseJson.getString("code");
                // 余额不足等业务异常需要向上传递
                if ("4004".equals(code)) {
                    throw new ActivityException(ActivityExceptionType.LOTTERY_INSUFFICIENT_BALANCE);
                }
                String errorMsg = responseJson.getString("msgKey");
                throw new RuntimeException("doFreezeAsset业务失败: " + errorMsg);
            }
        } catch (ActivityException e) {
            // 业务异常需要继续向上抛出
            throw e;
        } catch (Exception e) {
            log.warn("解doFreezeAsset响应异常，默认为成功: {}", e.getMessage());
            return false;
        }
        return true;
    }

    @Override
    public boolean unfreezeSubtractAsset(ExchangeTicketsDTO exchangeTicketsDTO) {
        return executeWithRetry("unfreezeSubtractAsset", () -> doUnfreezeSubtractAsset(exchangeTicketsDTO));
    }

    private boolean doUnfreezeSubtractAsset(ExchangeTicketsDTO exchangeTicketsDTO) {
        //这里要构造http请求调用其他服务
        String unfreezeSubtractUrl = kactivityProperties.getQuestsApiHost() + "/s2/member/unfreezeSubtract";
        HttpRequest httpRequest = HttpUtil.createPost(unfreezeSubtractUrl);
        httpRequest.addHeaders(buildHeaders(kactivityProperties.getSaasId()));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("saasId", exchangeTicketsDTO.getSaasId());
        jsonObject.put("amount", String.valueOf(exchangeTicketsDTO.getTransferOutAmount()));
        jsonObject.put("customerId", exchangeTicketsDTO.getUserId());
        jsonObject.put("businessId", generateOrderId(ActivityConstant.AssetOperateType.UNFREEZE_SUBTRACT, exchangeTicketsDTO.getUserId()));
        jsonObject.put("originalBusinessId", exchangeTicketsDTO.getBusinessId());
        jsonObject.put("businessType", exchangeTicketsDTO.getBusinessType());
        jsonObject.put("type", exchangeTicketsDTO.getTransferOutAssetType());
        jsonObject.put("desc", exchangeTicketsDTO.getDesc());

        httpRequest.body(jsonObject.toJSONString(), ContentType.JSON.toString());

        HttpResponse response = httpRequest.execute();
        if (response == null || response.getStatus() != HttpStatus.HTTP_OK) {
            log.error("doUnfreezeSubtractAsset调用失败: response={}", JSON.toJSONString(response));
            throw new RuntimeException("doUnfreezeSubtractAsset调用失败: " + (response != null ? response.getStatus() : "null response"));
        }

        if (org.apache.commons.lang3.StringUtils.isBlank(response.body())) {
            throw new RuntimeException("doUnfreezeSubtractAsset响应为空");
        }

        try {
            JSONObject responseJson = JSON.parseObject(response.body());
            if (responseJson.containsKey("success") && !responseJson.getBoolean("success")) {
                String errorMsg = responseJson.getString("msgKey");
                throw new RuntimeException("doUnfreezeSubtractAsset业务失败: " + errorMsg);
            }
        } catch (Exception e) {
            log.warn("解析doUnfreezeSubtractAsset响应异常，默认为成功: {}", e.getMessage());
            return false;
        }
        return true;
    }

    @Override
    public boolean unfreezeAsset(ExchangeTicketsDTO exchangeTicketsDTO) {
       return executeWithRetry("unfreezeAsset", () -> doUnfreezeAsset(exchangeTicketsDTO));
    }
    private boolean doUnfreezeAsset(ExchangeTicketsDTO exchangeTicketsDTO) {
        //这里要构造http请求调用其他服务
        String unfreezeUrl = kactivityProperties.getQuestsApiHost() + "/s2/member/unfreeze";
        HttpRequest httpRequest = HttpUtil.createPost(unfreezeUrl);
        httpRequest.addHeaders(buildHeaders(kactivityProperties.getSaasId()));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("saasId", exchangeTicketsDTO.getSaasId());
        jsonObject.put("customerId", exchangeTicketsDTO.getUserId());
        jsonObject.put("businessId", generateOrderId(ActivityConstant.AssetOperateType.UNFREEZE, exchangeTicketsDTO.getUserId()));
        jsonObject.put("businessType", exchangeTicketsDTO.getBusinessType());
        jsonObject.put("originalBusinessId", exchangeTicketsDTO.getBusinessId());
        jsonObject.put("amount", String.valueOf(exchangeTicketsDTO.getTransferOutAmount()));
        jsonObject.put("type", exchangeTicketsDTO.getTransferOutAssetType());
        jsonObject.put("desc", exchangeTicketsDTO.getDesc());

        httpRequest.body(jsonObject.toJSONString(), ContentType.JSON.toString());

        HttpResponse response = httpRequest.execute();
        if (response == null || response.getStatus() != HttpStatus.HTTP_OK) {
            log.error("doUnfreezeAsset调用失败: response={}", JSON.toJSONString(response));
            throw new RuntimeException("doUnfreezeAsset调用失败: " + (response != null ? response.getStatus() : "null response"));
        }

        if (org.apache.commons.lang3.StringUtils.isBlank(response.body())) {
            throw new RuntimeException("doUnfreezeAsset响应为空");
        }

        try {
            JSONObject responseJson = JSON.parseObject(response.body());
            if (responseJson.containsKey("success") && !responseJson.getBoolean("success")) {
                String errorMsg = responseJson.getString("msgKey");
                throw new RuntimeException("doUnfreezeAsset业务失败: " + errorMsg);
            }
        } catch (Exception e) {
            log.warn("解析doUnfreezeAsset响应异常，默认为成功: {}", e.getMessage());
            return false;
        }
        return true;
    }
}
