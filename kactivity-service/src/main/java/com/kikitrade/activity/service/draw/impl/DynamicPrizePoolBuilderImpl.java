package com.kikitrade.activity.service.draw.impl;

import com.kikitrade.activity.dal.tablestore.builder.PrizeConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.PrizeConfig;
import com.kikitrade.activity.service.draw.DynamicPrizePoolBuilder;
import com.kikitrade.activity.service.draw.filter.PrizePoolFilterStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 动态奖池构建器实现
 * 支持插件化的过滤策略管理，自动发现和注册所有过滤策略
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class DynamicPrizePoolBuilderImpl implements DynamicPrizePoolBuilder {
    
    @Resource
    private PrizeConfigBuilder prizeConfigBuilder;
    
    @Autowired
    private ApplicationContext applicationContext;
    
    /**
     * 策略注册表，key为策略名称，value为策略实例
     */
    private final Map<String, PrizePoolFilterStrategy> strategies = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        // 自动发现和注册所有过滤策略
        autoRegisterStrategies();
        log.info("动态奖池构建器初始化完成，已注册策略数量: {}", strategies.size());
    }
    
    @Override
    public List<PrizeConfig> buildDynamicPool(String userId, String prizePoolCode, String saasId) {
        log.info("构建动态奖池: userId={}, prizePoolCode={}, saasId={}", userId, prizePoolCode, saasId);
        
        try {
            // 1. 获取通用奖品（基础奖品）
            List<PrizeConfig> commonPrizes = prizeConfigBuilder.findCommonPrizesByPoolCode(saasId, prizePoolCode);
            List<PrizeConfig> dynamicPool = new ArrayList<>(commonPrizes);
            log.info("通用奖品数量: {}", commonPrizes.size());
            
            // 2. 按优先级应用所有启用的过滤策略
            List<PrizePoolFilterStrategy> enabledStrategies = strategies.values().stream()
                    .filter(PrizePoolFilterStrategy::isEnabled)
                    .sorted(Comparator.comparingInt(PrizePoolFilterStrategy::getPriority))
                    .collect(Collectors.toList());
            
            log.info("启用的过滤策略数量: {}", enabledStrategies.size());
            
            for (PrizePoolFilterStrategy strategy : enabledStrategies) {
                try {
                    String filterValue = strategy.getUserFilterValue(userId, prizePoolCode);
                    if (filterValue != null) {
                        List<PrizeConfig> filteredPrizes = strategy.filterPrizes(prizePoolCode, saasId, filterValue);
                        dynamicPool.addAll(filteredPrizes);
                        log.info("策略 {} 添加奖品数量: {}, filterValue: {}", 
                                strategy.getStrategyName(), filteredPrizes.size(), filterValue);
                    } else {
                        log.debug("策略 {} 的过滤值为空，跳过", strategy.getStrategyName());
                    }
                } catch (Exception e) {
                    log.error("应用过滤策略失败: strategy={}, userId={}", 
                            strategy.getStrategyName(), userId, e);
                    // 策略执行失败不影响其他策略和基础奖品
                }
            }
            
            log.info("动态奖池构建完成，总奖品数量: {}", dynamicPool.size());
            return dynamicPool;
            
        } catch (Exception e) {
            log.error("构建动态奖池失败: userId={}, prizePoolCode={}", userId, prizePoolCode, e);
            
//            // 发生异常时返回通用奖品，确保基本功能可用
//            try {
//                List<PrizeConfig> fallbackPrizes = prizeConfigBuilder.findCommonPrizesByPoolCode(saasId, prizePoolCode);
//                log.warn("使用降级方案，返回通用奖品数量: {}", fallbackPrizes.size());
//                return fallbackPrizes;
//            } catch (Exception fallbackException) {
//                log.error("降级方案也失败了", fallbackException);
//                return new ArrayList<>();
//            }
            return new ArrayList<>();
        }
    }
    
    @Override
    public void registerFilterStrategy(PrizePoolFilterStrategy strategy) {
        if (strategy == null) {
            log.warn("尝试注册空的过滤策略");
            return;
        }
        
        String strategyName = strategy.getStrategyName();
        if (strategyName == null || strategyName.trim().isEmpty()) {
            log.warn("过滤策略名称为空，跳过注册: {}", strategy.getClass().getSimpleName());
            return;
        }
        
        PrizePoolFilterStrategy existingStrategy = strategies.put(strategyName, strategy);
        if (existingStrategy != null) {
            log.warn("覆盖已存在的过滤策略: {}", strategyName);
        } else {
            log.info("注册过滤策略: {}, 优先级: {}", strategyName, strategy.getPriority());
        }
    }
    
    @Override
    public void removeFilterStrategy(String strategyName) {
        if (strategyName == null || strategyName.trim().isEmpty()) {
            log.warn("策略名称为空，无法移除");
            return;
        }
        
        PrizePoolFilterStrategy removedStrategy = strategies.remove(strategyName);
        if (removedStrategy != null) {
            log.info("移除过滤策略: {}", strategyName);
        } else {
            log.warn("要移除的策略不存在: {}", strategyName);
        }
    }
    
    @Override
    public List<PrizePoolFilterStrategy> getAllStrategies() {
        return new ArrayList<>(strategies.values());
    }
    
    @Override
    public PrizePoolFilterStrategy getStrategy(String strategyName) {
        return strategies.get(strategyName);
    }
    
    @Override
    public boolean hasStrategy(String strategyName) {
        return strategies.containsKey(strategyName);
    }
    
    /**
     * 自动发现和注册所有过滤策略
     */
    private void autoRegisterStrategies() {
        try {
            Map<String, PrizePoolFilterStrategy> strategyBeans = 
                    applicationContext.getBeansOfType(PrizePoolFilterStrategy.class);
            
            log.info("发现过滤策略Bean数量: {}", strategyBeans.size());
            
            for (Map.Entry<String, PrizePoolFilterStrategy> entry : strategyBeans.entrySet()) {
                String beanName = entry.getKey();
                PrizePoolFilterStrategy strategy = entry.getValue();
                
                try {
                    registerFilterStrategy(strategy);
                    log.debug("自动注册策略成功: beanName={}, strategyName={}", 
                            beanName, strategy.getStrategyName());
                } catch (Exception e) {
                    log.error("自动注册策略失败: beanName={}", beanName, e);
                }
            }
            
        } catch (Exception e) {
            log.error("自动发现过滤策略失败", e);
        }
    }
}
