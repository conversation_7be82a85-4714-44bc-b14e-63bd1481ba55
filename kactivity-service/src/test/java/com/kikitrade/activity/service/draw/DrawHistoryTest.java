package com.kikitrade.activity.service.draw;

import com.kikitrade.activity.dal.tablestore.builder.DrawHistoryBuilder;
import com.kikitrade.activity.dal.tablestore.model.DrawHistory;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 抽奖历史记录功能测试
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@SpringBootTest
@ActiveProfiles("test")
public class DrawHistoryTest {

    @Resource
    private DrawHistoryBuilder drawHistoryBuilder;

    /**
     * 测试批量插入抽奖历史记录
     */
    @Test
    public void testBatchInsertDrawHistory() {
        // 准备测试数据
        String userId = "test-user-" + System.currentTimeMillis();
        String batchTransactionId = "draw-batch-" + UUID.randomUUID().toString();
        String prizePoolCode = "TEST_POOL";
        String saasId = "test-saas";
        int drawCount = 5;
        
        List<DrawHistory> historyRecords = new ArrayList<>();
        long currentTime = System.currentTimeMillis();
        
        // 模拟批量抽奖生成历史记录
        for (int i = 0; i < drawCount; i++) {
            String eventId = "evt-" + UUID.randomUUID().toString();
            
            DrawHistory drawHistory = new DrawHistory();
            drawHistory.setUserId(userId);
            drawHistory.setEventId(eventId);
            drawHistory.setUpstream_transaction_id(batchTransactionId);
            drawHistory.setBatchTransactionId(batchTransactionId);
            drawHistory.setPrizePoolCode(prizePoolCode);
            drawHistory.setPrizeId("" + 1000L + i);
            drawHistory.setPrizeName("测试奖品" + (i + 1));
            drawHistory.setDrawTime(currentTime);
            drawHistory.setPrizeType("ITEM");
            drawHistory.setPrizeQuantity(1);
            drawHistory.setPrizeItemId("item-" + (1000 + i));
            drawHistory.setSaasId(saasId);
            
            historyRecords.add(drawHistory);
        }
        
        // 执行批量插入
        System.out.println("开始批量插入抽奖历史记录测试...");
        System.out.println("用户ID: " + userId);
        System.out.println("批量交易ID: " + batchTransactionId);
        System.out.println("记录数量: " + historyRecords.size());
        
        boolean result = drawHistoryBuilder.batchInsert(historyRecords);
        
        System.out.println("批量插入结果: " + (result ? "成功" : "失败"));
        
        if (result) {
            System.out.println("✅ 批量抽奖历史记录落库测试通过");
            
            // 验证数据是否正确插入
            try {
                Thread.sleep(2000); // 等待数据同步
                
                // 查询验证
                var queryResult = drawHistoryBuilder.findByBatchTransactionId(batchTransactionId, null, 10);
                System.out.println("查询到的记录数量: " + queryResult.getRows().size());
                
                if (queryResult.getRows().size() == drawCount) {
                    System.out.println("✅ 数据查询验证通过");
                } else {
                    System.out.println("❌ 数据查询验证失败: 期望" + drawCount + "条，实际" + queryResult.getRows().size() + "条");
                }
                
            } catch (Exception e) {
                System.out.println("查询验证异常: " + e.getMessage());
            }
        } else {
            System.out.println("❌ 批量抽奖历史记录落库测试失败");
        }
    }

    /**
     * 测试单条插入抽奖历史记录
     */
    @Test
    public void testSingleInsertDrawHistory() {
        String userId = "test-user-single-" + System.currentTimeMillis();
        String eventId = "evt-" + UUID.randomUUID().toString();
        String batchTransactionId = "draw-batch-" + UUID.randomUUID().toString();
        
        DrawHistory drawHistory = new DrawHistory();
        drawHistory.setUserId(userId);
        drawHistory.setEventId(eventId);
        drawHistory.setUpstream_transaction_id(batchTransactionId);
        drawHistory.setBatchTransactionId(batchTransactionId);
        drawHistory.setPrizePoolCode("TEST_POOL");
        drawHistory.setPrizeId("" + 2000L);
        drawHistory.setPrizeName("单次测试奖品");
        drawHistory.setDrawTime(System.currentTimeMillis());
        drawHistory.setPrizeType("ITEM");
        drawHistory.setPrizeQuantity(1);
        drawHistory.setPrizeItemId("item-2000");
        drawHistory.setSaasId("test-saas");
        
        System.out.println("开始单条插入抽奖历史记录测试...");
        System.out.println("用户ID: " + userId);
        System.out.println("事件ID: " + eventId);
        
        boolean result = drawHistoryBuilder.insert(drawHistory);
        
        System.out.println("单条插入结果: " + (result ? "成功" : "失败"));
        
        if (result) {
            System.out.println("✅ 单条抽奖历史记录插入测试通过");
            
            // 验证数据
            try {
                Thread.sleep(1000);
                DrawHistory queryResult = drawHistoryBuilder.findByPrimaryKey(userId, eventId);
                if (queryResult != null) {
                    System.out.println("✅ 单条数据查询验证通过");
                    System.out.println("查询到的奖品名称: " + queryResult.getPrizeName());
                } else {
                    System.out.println("❌ 单条数据查询验证失败: 未找到记录");
                }
            } catch (Exception e) {
                System.out.println("单条查询验证异常: " + e.getMessage());
            }
        } else {
            System.out.println("❌ 单条抽奖历史记录插入测试失败");
        }
    }
}
