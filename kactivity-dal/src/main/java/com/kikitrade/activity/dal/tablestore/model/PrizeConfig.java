package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 奖品配置表
 * 对应技术规格书中的 prize_config 表
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Table(name = "prize_config")
public class PrizeConfig implements Serializable {

    public static final String SEARCH_PRIZE_CONFIG = "search_prize_config";

    /**
     * SaaS ID（分区键）
     */
    @PartitionKey(name = "saas_id")
    private String saasId;

    /**
     * 所属奖池编码（排序键1）
     */
    @PartitionKey(name = "prize_pool_code")
    private String prizePoolCode;

    /**
     * 唯一ID（排序键2）
     */
    @PartitionKey(name = "prize_id", value = 2)
    private String prizeId;

    /**
     * 关联的偏好类型 (如 SELECTED_HERO, NULL表示通用)
     */
    @Column(name = "preference_type")
    @SearchIndex(name = SEARCH_PRIZE_CONFIG, column = "preference_type")
    private String preferenceType;

    /**
     * 关联的偏好值 (如 HERO_A_001)
     */
    @Column(name = "preference_value")
    @SearchIndex(name = SEARCH_PRIZE_CONFIG, column = "preference_value")
    private String preferenceValue;

    /**
     * 奖品名称
     */
    @Column(name = "reward_name")
    private String rewardName;

    @Column(name = "reward_icon")
    private String rewardIcon;

    /**
     * 奖励类型 (ITEM, CURRENCY, GIFT_PACK)
     */
    @Column(name = "reward_type")
    private String rewardType;

    /**
     * 引用: 奖励的业务ID (关联 item_master_config.item_id)
     * 通过此字段关联物品主数据表获取物品详细信息
     */
    @Column(name = "reward_item_id")
    private String rewardItemId;

    /**
     * 每次中奖发放数量
     */
    @Column(name = "reward_quantity", type = Column.Type.INTEGER)
    private Integer rewardQuantity;

    /**
     * 中奖概率
     */
    @Column(name = "winning_probability", type = Column.Type.DOUBLE)
    private BigDecimal winningProbability;

    /**
     * 库存数量 (-1 表示无限)
     */
    @Column(name = "stock_quantity", type = Column.Type.INTEGER)
    private Integer stockQuantity;

    /**
     * 是否生效
     */
    @Column(name = "is_active", type = Column.Type.BOOLEAN)
    private Boolean isActive;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    private Long updateTime;


}