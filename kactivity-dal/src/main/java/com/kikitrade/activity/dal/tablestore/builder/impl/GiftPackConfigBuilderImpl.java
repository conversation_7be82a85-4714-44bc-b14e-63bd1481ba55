package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.kikitrade.activity.dal.tablestore.builder.GiftPackConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.GiftPackConfig;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.kikitrade.activity.dal.tablestore.model.GiftPackConfig.SEARCH_GIFT_PACK_CONFIG;

/**
 * 礼包内容规则表数据访问层实现
 * 优化：适配新的三元主键 (saas_id, pack_id, rule_id) 结构
 * 
 * <AUTHOR>
 * @date 2025/9/4 10:43
 */
@Slf4j
@Component
public class GiftPackConfigBuilderImpl extends WideColumnStoreBuilder<GiftPackConfig> implements GiftPackConfigBuilder {
    /**
     * 根据礼包ID查询礼包配置规则
     * 使用范围查询，以saas_id为分区键
     *
     * @param packId 礼包ID
     * @param saasId SaaS ID
     * @return 礼包配置规则列表
     */
    @Override
    public List<GiftPackConfig> findByPackId(String packId, String saasId) {
        try {
            // 使用范围查询，指定分区键和部分排序键
            List<RangeQueryParameter> parameters = new ArrayList<>();
            parameters.add(new RangeQueryParameter("saas_id", PrimaryKeyValue.fromString(saasId)));
            parameters.add(new RangeQueryParameter("pack_id", PrimaryKeyValue.fromString(packId)));
            parameters.add(new RangeQueryParameter("rule_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
            return rangeQuery(parameters);
        } catch (Exception e) {
            log.error("根据礼包ID查询礼包配置失败: packId={}, saasId={}", packId, saasId, e);
            return List.of();
        }
    }

    /**
     * 根据三元主键查询单个礼包配置规则
     *
     * @param saasId SaaS ID
     * @param packId 礼包ID
     * @param ruleId 规则ID
     * @return 礼包配置规则
     */
    @Override
    public GiftPackConfig findBySaasIdAndPackIdAndRuleId(String saasId, String packId, String ruleId) {
        try {
            GiftPackConfig giftPackConfig = new GiftPackConfig();
            giftPackConfig.setSaasId(saasId);
            giftPackConfig.setPackId(packId);
            giftPackConfig.setRuleId(ruleId);
            return getRow(giftPackConfig);
        } catch (Exception e) {
            log.error("根据三元主键查询礼包配置失败: saasId={}, packId={}, ruleId={}", saasId, packId, ruleId, e);
            return null;
        }
    }

    /**
     * 根据礼包ID和规则类型查询配置
     *
     * @param packId   礼包ID
     * @param ruleType 规则类型
     * @param saasId   SaaS ID
     * @return 礼包配置规则列表
     */
    @Override
    public List<GiftPackConfig> findByPackIdAndRuleType(String packId, String ruleType, String saasId) {
        try {
            // 先获取所有规则，然后过滤
            List<GiftPackConfig> allRules = findByPackId(packId, saasId);
            return allRules.stream()
                    .filter(config -> ruleType.equals(config.getRuleType()))
                    .filter(config -> saasId.equals(config.getSaasId()))
                    .toList();
        } catch (Exception e) {
            log.error("根据礼包ID和规则类型查询失败: packId={}, ruleType={}, saasId={}", packId, ruleType, saasId, e);
            return List.of();
        }
    }

    @Override
    public boolean insert(GiftPackConfig giftPackConfig) {
        try {
            giftPackConfig.setCreateTime(System.currentTimeMillis());
            return putRow(giftPackConfig, RowExistenceExpectation.EXPECT_NOT_EXIST);
        } catch (Exception e) {
            log.error("插入礼包配置失败", e);
            return false;
        }
    }

    @Override
    public boolean update(GiftPackConfig giftPackConfig) {
        try {
            Condition condition = new Condition();
            condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
            return super.updateRow(giftPackConfig, condition);
        } catch (Exception e) {
            log.error("更新礼包配置失败", e);
            return false;
        }
    }

    @Override
    public boolean delete(String saasId, String packId, String ruleId) {
        try {
            GiftPackConfig giftPackConfig = new GiftPackConfig();
            giftPackConfig.setSaasId(saasId);
            giftPackConfig.setPackId(packId);
            giftPackConfig.setRuleId(ruleId);

            return super.deleteRow(giftPackConfig);
        } catch (Exception e) {
            log.error("删除礼包配置失败: saasId={}, packId={}, ruleId={}", saasId, packId, ruleId, e);
            return false;
        }
    }
}
