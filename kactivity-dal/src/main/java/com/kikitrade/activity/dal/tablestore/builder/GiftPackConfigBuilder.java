package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.GiftPackConfig;

import java.util.List;

/**
 * 礼包内容规则表数据访问层
 * 优化：使用三元复合主键 (saas_id, pack_id, rule_id)
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface GiftPackConfigBuilder {

    /**
     * 根据礼包ID查询礼包配置规则
     * 
     * @param packId 礼包ID
     * @param saasId SaaS ID
     * @return 礼包配置规则列表
     */
    List<GiftPackConfig> findByPackId(String packId, String saasId);

    /**
     * 根据三元主键查询单个礼包配置规则
     * 
     * @param saasId SaaS ID
     * @param packId 礼包ID
     * @param ruleId 规则ID
     * @return 礼包配置规则
     */
    GiftPackConfig findBySaasIdAndPackIdAndRuleId(String saasId, String packId, String ruleId);

    /**
     * 插入礼包配置规则
     * 
     * @param giftPackConfig 礼包配置规则
     * @return 是否成功
     */
    boolean insert(GiftPackConfig giftPackConfig);

    /**
     * 更新礼包配置规则
     * 
     * @param giftPackConfig 礼包配置规则
     * @return 是否成功
     */
    boolean update(GiftPackConfig giftPackConfig);

    /**
     * 根据三元主键删除礼包配置规则
     * 
     * @param saasId SaaS ID
     * @param packId 礼包ID
     * @param ruleId 规则ID
     * @return 是否成功
     */
    boolean delete(String saasId, String packId, String ruleId);

    /**
     * 根据礼包ID和规则类型查询配置
     * 
     * @param packId 礼包ID
     * @param ruleType 规则类型
     * @param saasId SaaS ID
     * @return 礼包配置规则列表
     */
    List<GiftPackConfig> findByPackIdAndRuleType(String packId, String ruleType, String saasId);
}
