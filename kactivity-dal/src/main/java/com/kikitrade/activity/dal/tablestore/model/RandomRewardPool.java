package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * 随机奖励池表数据模型
 * 对应技术规格书中的 random_reward_pool 表
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Table(name = "random_reward_pool")
public class RandomRewardPool implements Serializable {

    public static final String SEARCH_RANDOM_REWARD_POOL = "search_random_reward_pool";

    /**
     * SaaS ID（分区键）
     */
    @PartitionKey(name = "saas_id")
    private String saasId;

    /**
     * 随机池ID（排序键1）
     */
    @PartitionKey(name = "pool_id", value = 1)
    private String poolId;

    /**
     * 引用: 物品ID（排序键2）- 关联 item_master_config.item_id
     * 通过此字段关联物品主数据表获取物品详细信息
     */
    @PartitionKey(name = "item_id", value = 2)
    private String itemId;

    /**
     * 最小数量
     */
    @Column(name = "quantity_min", type = Column.Type.INTEGER)
    private Integer quantityMin;

    /**
     * 最大数量
     */
    @Column(name = "quantity_max", type = Column.Type.INTEGER)
    private Integer quantityMax;

    /**
     * 权重 (用于随机抽取)
     */
    @Column(name = "weight", type = Column.Type.INTEGER)
    private Integer weight;

    /**
     * 是否启用
     */
    @Column(name = "is_active", type = Column.Type.BOOLEAN)
    private Boolean isActive;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    private Long updateTime;

}
