package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * 物品主数据表
 * 对应技术规格书中的 item_master_config 表
 * 存储系统中所有物品的基本信息，避免在各表中冗余存储
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Table(name = "item_master_config")
public class ItemMasterConfig implements Serializable {

    public static final String SEARCH_ITEM_MASTER_CONFIG = "search_item_master_config";

    /**
     * SaaS ID（分区键）
     */
    @PartitionKey(name = "saas_id")
    private String saasId;

    /**
     * 系统内部物品唯一ID（排序键）
     */
    @PartitionKey(name = "item_id", value = 1)
    private String itemId;

    /**
     * 物品的官方名称
     */
    @Column(name = "item_name")
    @SearchIndex(name = SEARCH_ITEM_MASTER_CONFIG, column = "item_name")
    private String itemName;

    /**
     * 物品的官方图标URL
     */
    @Column(name = "item_icon")
    private String itemIcon;

    /**
     * 物品的类型 (ITEM, CURRENCY, GIFT_PACK, AVATAR_FRAME等)
     */
    @Column(name = "item_type")
    @SearchIndex(name = SEARCH_ITEM_MASTER_CONFIG, column = "item_type")
    private String itemType;

    /**
     * 游戏/外部系统的道具ID (NULL表示非实体道具)
     */
    @Column(name = "external_item_id")
    private String externalItemId;

    /**
     * 物品的描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 该物品是否在整个系统中可用
     */
    @Column(name = "is_active", type = Column.Type.BOOLEAN)
    @SearchIndex(name = SEARCH_ITEM_MASTER_CONFIG, column = "is_active")
    private Boolean isActive;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", type = Column.Type.INTEGER)
    private Long updateTime;
}
