package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.ItemMasterConfig;

import java.util.List;

/**
 * 物品主数据表数据访问层接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface ItemMasterConfigBuilder {

    /**
     * 根据物品ID查询物品主数据
     * 
     * @param saasId SaaS ID
     * @param itemId 物品ID
     * @return 物品主数据
     */
    ItemMasterConfig findByItemId(String saasId, String itemId);

    /**
     * 根据物品类型查询活跃的物品列表
     * 
     * @param saasId SaaS ID
     * @param itemType 物品类型
     * @return 物品主数据列表
     */
    List<ItemMasterConfig> findActiveByItemType(String saasId, String itemType);

    /**
     * 根据外部物品ID查询物品主数据
     * 
     * @param saasId SaaS ID
     * @param externalItemId 外部物品ID
     * @return 物品主数据
     */
    ItemMasterConfig findByExternalItemId(String saasId, String externalItemId);

    /**
     * 查询所有活跃的物品
     * 
     * @param saasId SaaS ID
     * @return 物品主数据列表
     */
    List<ItemMasterConfig> findActiveBySaasId(String saasId);

    /**
     * 批量查询物品主数据
     * 
     * @param saasId SaaS ID
     * @param itemIds 物品ID列表
     * @return 物品主数据列表
     */
    List<ItemMasterConfig> findByItemIds(String saasId, List<String> itemIds);

    /**
     * 插入物品主数据
     * 
     * @param itemMasterConfig 物品主数据
     * @return 是否成功
     */
    boolean insert(ItemMasterConfig itemMasterConfig);

    /**
     * 更新物品主数据
     * 
     * @param itemMasterConfig 物品主数据
     * @return 是否成功
     */
    boolean update(ItemMasterConfig itemMasterConfig);

    /**
     * 删除物品主数据（软删除，设置为非活跃）
     * 
     * @param saasId SaaS ID
     * @param itemId 物品ID
     * @return 是否成功
     */
    boolean deactivate(String saasId, String itemId);
}
