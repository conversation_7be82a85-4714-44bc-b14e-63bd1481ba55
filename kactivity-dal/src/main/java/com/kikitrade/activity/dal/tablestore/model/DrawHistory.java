package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * 抽奖历史记录表
 * 对应技术规格书中的 draw_history 表
 * 用于记录所有抽奖历史，支持批量抽奖的事务ID
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Table(name = "draw_history")
public class DrawHistory implements Serializable {

    public static final String SEARCH_DRAW_HISTORY = "search_draw_history";

    /**
     * 用户ID（分区键）
     */
    @PartitionKey(name = "user_id")
    private String userId;

    /**
     * 抽奖事件唯一ID（排序键）
     */
    @PartitionKey(name = "event_id", value = 1)
    private String eventId;

    /**
     * 上游交易ID
     */
    @Column(name = "upstream_transaction_id")
    @SearchIndex(name = SEARCH_DRAW_HISTORY, column = "upstream_transaction_id")
    private String upstream_transaction_id;

    /**
     * 批量抽奖的唯一交易ID
     */
    @Column(name = "batch_transaction_id")
    @SearchIndex(name = SEARCH_DRAW_HISTORY, column = "batch_transaction_id")
    private String batchTransactionId;

    /**
     * 抽奖时所在的奖池编码
     */
    @Column(name = "prize_pool_code")
    @SearchIndex(name = SEARCH_DRAW_HISTORY, column = "prize_pool_code")
    private String prizePoolCode;

    /**
     * 中奖的奖品ID
     */
    @Column(name = "prize_id")
    private String prizeId;

    /**
     * 中奖的奖品名称
     */
    @Column(name = "prize_name")
    private String prizeName;

    /**
     * 抽奖时间
     */
    @Column(name = "draw_time", type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_DRAW_HISTORY, column = "draw_time")
    private Long drawTime;

    /**
     * 奖品类型
     */
    @Column(name = "prize_type")
    private String prizeType;

    /**
     * 奖品数量
     */
    @Column(name = "prize_quantity", type = Column.Type.INTEGER)
    private Integer prizeQuantity;

    /**
     * 奖品物品ID
     */
    @Column(name = "prize_item_id")
    private String prizeItemId;

    /**
     * 创建时间
     */
    @Column(name = "create_time", type = Column.Type.INTEGER)
    private Long createTime;

    /**
     * SaaS ID
     */
    @Column(name = "saas_id")
    @SearchIndex(name = SEARCH_DRAW_HISTORY, column = "saas_id")
    private String saasId;
}